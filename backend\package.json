{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "json-server": "^1.0.0-beta.3", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}